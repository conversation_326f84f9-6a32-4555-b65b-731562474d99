#include <Arduino.h>

// Serial stub for ESP32-C3 to resolve linker issues
// This provides a minimal Serial object when USB CDC is disabled

#if !defined(ARDUINO_USB_CDC_ON_BOOT) || ARDUINO_USB_CDC_ON_BOOT == 0

class SerialStub : public Stream {
public:
    void begin(unsigned long baud) {}
    void end() {}
    int available() { return 0; }
    int read() { return -1; }
    int peek() { return -1; }
    void flush() {}
    size_t write(uint8_t) { return 1; }
    size_t write(const uint8_t *buffer, size_t size) { return size; }
    operator bool() { return false; }
};

SerialStub Serial;

#endif
